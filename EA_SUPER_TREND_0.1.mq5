//+------------------------------------------------------------------+
//|                                                SuperTrend_EA.mq5 |
//|                                  Copyright 2024, Augment Agent   |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Agent"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "SuperTrend Expert Advisor with trend reversal strategy"

//--- Include necessary libraries
#include <Trade\Trade.mqh>

//--- Input parameters
input group "=== SuperTrend Settings ==="
input int      ATR_Period = 10;           // ATR Period
input double   Multiplier = 3.0;          // SuperTrend Multiplier

input group "=== Trading Settings ==="
input double   LotSize = 0.1;             // Lot Size
input ENUM_TIMEFRAMES Timeframe = PERIOD_M15; // Trading Timeframe
input int      StopLoss = 2000;           // Stop Loss in points
input int      TakeProfit = 2000;         // Take Profit in points

input group "=== Optional Settings ==="
input bool     UseTrailingStop = true;    // Use Trailing Stop
input int      TrailingStop = 250;        // Trailing Stop in points
input bool     UseTimeFilter = true;      // Use Time Filter
input int      StartHour = 8;             // Trading Start Hour
input int      EndHour = 17;              // Trading End Hour

input group "=== Martingale Settings ==="
input bool     UseMartingale = true;      // Use Martingale Position Management
input double   MartingaleLotSize = 0.03;  // Martingale Lot Size (larger than base)
input int      TriggerDistance = 15000;   // Distance from entry to trigger martingale (points)
input int      MartingaleTP_Points = 10000; // Martingale TP target (points)
input int      FinalSL_Points = 5000;     // Final SL from old SL (points)
input bool     DisableSuperTrendExit = true; // Disable SuperTrend exit signals when using martingale

input group "=== Risk Management ==="
input int      MaxSpread = 50;            // Maximum Spread in points
input int      Slippage = 10;             // Maximum Slippage in points
input bool     OneTradePerSignal = true;  // Allow only one trade per signal

//--- Global variables
CTrade trade;
int atr_handle;
double atr_buffer[];
double high_buffer[], low_buffer[], close_buffer[];
double supertrend_upper[], supertrend_lower[];
bool supertrend_trend[];
int bars_total;
datetime last_bar_time;
datetime last_trade_time;        // Time of last trade to prevent multiple trades per bar
bool last_signal_processed;      // Flag to track if current signal was already processed
int last_trade_type;             // Track last trade type: 0=none, 1=buy, 2=sell
bool waiting_for_opposite_signal; // Flag to wait for opposite signal after trade

//--- Martingale Variables
bool martingale_active;           // Flag if martingale position is active
double first_position_price;     // Entry price of first position
double first_position_sl;        // Original SL of first position
ulong first_position_ticket;     // Ticket of first position
ulong martingale_ticket;         // Ticket of martingale position
bool martingale_triggered;       // Flag if martingale was already triggered

//--- Magic number for orders
#define MAGIC_NUMBER 123456

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set trade parameters
    trade.SetExpertMagicNumber(MAGIC_NUMBER);
    trade.SetDeviationInPoints(Slippage);
    
    //--- Create ATR indicator handle
    atr_handle = iATR(_Symbol, Timeframe, ATR_Period);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator handle");
        return INIT_FAILED;
    }
    
    //--- Initialize arrays
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(high_buffer, true);
    ArraySetAsSeries(low_buffer, true);
    ArraySetAsSeries(close_buffer, true);
    ArraySetAsSeries(supertrend_upper, true);
    ArraySetAsSeries(supertrend_lower, true);
    ArraySetAsSeries(supertrend_trend, true);
    
    //--- Get initial bar count
    bars_total = iBars(_Symbol, Timeframe);
    last_bar_time = iTime(_Symbol, Timeframe, 0);

    //--- Initialize trade control variables
    last_trade_time = 0;
    last_signal_processed = false;
    last_trade_type = 0;              // 0=none, 1=buy, 2=sell
    waiting_for_opposite_signal = false;

    //--- Initialize martingale variables
    martingale_active = false;
    first_position_price = 0;
    first_position_sl = 0;
    first_position_ticket = 0;
    martingale_ticket = 0;
    martingale_triggered = false;

    Print("SuperTrend EA with Distance Martingale initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handle
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
    
    Print("SuperTrend EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check for new bar
    if(!IsNewBar())
        return;
    
    //--- Check time filter
    if(UseTimeFilter && !IsTimeToTrade())
        return;
    
    //--- Check spread
    if(GetSpread() > MaxSpread)
        return;
    
    //--- Calculate SuperTrend
    if(!CalculateSuperTrend())
        return;
    
    //--- Handle trailing stop
    if(UseTrailingStop)
        HandleTrailingStop();

    //--- Check martingale conditions
    if(UseMartingale)
        CheckMartingaleConditions();

    //--- Check for entry signals
    CheckEntrySignals();

    //--- Check for exit signals
    CheckExitSignals();
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime current_time = iTime(_Symbol, Timeframe, 0);
    if(current_time != last_bar_time)
    {
        last_bar_time = current_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    if(StartHour <= EndHour)
        return (dt.hour >= StartHour && dt.hour < EndHour);
    else
        return (dt.hour >= StartHour || dt.hour < EndHour);
}

//+------------------------------------------------------------------+
//| Get current spread in points                                     |
//+------------------------------------------------------------------+
int GetSpread()
{
    return (int)((Ask() - Bid()) / _Point);
}

//+------------------------------------------------------------------+
//| Calculate SuperTrend indicator                                   |
//+------------------------------------------------------------------+
bool CalculateSuperTrend()
{
    //--- Get required data
    if(CopyBuffer(atr_handle, 0, 0, 3, atr_buffer) < 3)
        return false;
    
    if(CopyHigh(_Symbol, Timeframe, 0, 3, high_buffer) < 3)
        return false;
    
    if(CopyLow(_Symbol, Timeframe, 0, 3, low_buffer) < 3)
        return false;
    
    if(CopyClose(_Symbol, Timeframe, 0, 3, close_buffer) < 3)
        return false;
    
    //--- Resize SuperTrend arrays
    ArrayResize(supertrend_upper, 3);
    ArrayResize(supertrend_lower, 3);
    ArrayResize(supertrend_trend, 3);
    
    //--- Calculate SuperTrend for each bar
    for(int i = 2; i >= 0; i--)
    {
        double hl2 = (high_buffer[i] + low_buffer[i]) / 2.0;
        double atr_value = atr_buffer[i];
        
        //--- Calculate basic upper and lower bands
        double basic_upper = hl2 + (Multiplier * atr_value);
        double basic_lower = hl2 - (Multiplier * atr_value);
        
        //--- Calculate final upper and lower bands
        if(i == 2) // First calculation
        {
            supertrend_upper[i] = basic_upper;
            supertrend_lower[i] = basic_lower;
        }
        else
        {
            supertrend_upper[i] = (basic_upper < supertrend_upper[i+1] || close_buffer[i+1] > supertrend_upper[i+1]) ? 
                                  basic_upper : supertrend_upper[i+1];
            
            supertrend_lower[i] = (basic_lower > supertrend_lower[i+1] || close_buffer[i+1] < supertrend_lower[i+1]) ? 
                                  basic_lower : supertrend_lower[i+1];
        }
        
        //--- Determine trend direction
        if(i == 2) // First calculation
        {
            supertrend_trend[i] = close_buffer[i] <= supertrend_lower[i];
        }
        else
        {
            if(supertrend_trend[i+1] && close_buffer[i] > supertrend_lower[i])
                supertrend_trend[i] = false; // Bullish
            else if(!supertrend_trend[i+1] && close_buffer[i] < supertrend_upper[i])
                supertrend_trend[i] = true;  // Bearish
            else
                supertrend_trend[i] = supertrend_trend[i+1]; // No change
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check for entry signals                                          |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
    //--- Check if we already have a position
    if(PositionSelect(_Symbol))
        return;

    //--- Check for pending orders
    if(HasPendingOrders())
        return;

    //--- Check for trend change signals
    if(ArraySize(supertrend_trend) < 2)
        return;

    //--- Detect signals
    bool current_signal_buy = (supertrend_trend[1] == true && supertrend_trend[0] == false);
    bool current_signal_sell = (supertrend_trend[1] == false && supertrend_trend[0] == true);

    //--- Prevent multiple trades on the same bar
    datetime current_bar_time = iTime(_Symbol, Timeframe, 0);
    if(OneTradePerSignal && last_trade_time == current_bar_time)
        return;

    //--- OneTradePerSignal Logic: Wait for opposite signal
    if(OneTradePerSignal && waiting_for_opposite_signal)
    {
        // If last trade was BUY (1), only allow SELL signals
        if(last_trade_type == 1 && !current_signal_sell)
            return;

        // If last trade was SELL (2), only allow BUY signals
        if(last_trade_type == 2 && !current_signal_buy)
            return;
    }

    //--- Buy signal: SuperTrend changes from Red to Green (bearish to bullish)
    if(current_signal_buy)
    {
        if(OpenBuyPosition())
        {
            if(OneTradePerSignal)
            {
                last_trade_time = current_bar_time;
                // Reset flags from previous trade and set new state
                waiting_for_opposite_signal = true;
                last_trade_type = 1;  // Remember this was a BUY trade - now wait for SELL
            }
            Print("Buy signal processed at ", TimeToString(current_bar_time));
        }
    }
    //--- Sell signal: SuperTrend changes from Green to Red (bullish to bearish)
    else if(current_signal_sell)
    {
        if(OpenSellPosition())
        {
            if(OneTradePerSignal)
            {
                last_trade_time = current_bar_time;
                // Reset flags from previous trade and set new state
                waiting_for_opposite_signal = true;
                last_trade_type = 2;  // Remember this was a SELL trade - now wait for BUY
            }
            Print("Sell signal processed at ", TimeToString(current_bar_time));
        }
    }
}

//+------------------------------------------------------------------+
//| Check for exit signals                                           |
//+------------------------------------------------------------------+
void CheckExitSignals()
{
    //--- Exit signals disabled when using martingale (if setting enabled)
    //--- Martingale system will handle all exits
    if(UseMartingale && DisableSuperTrendExit)
        return;

    //--- Check if we have a position
    if(!PositionSelect(_Symbol))
        return;

    long position_type = PositionGetInteger(POSITION_TYPE);

    //--- Close Buy when SuperTrend turns Red (bearish)
    if(position_type == POSITION_TYPE_BUY && supertrend_trend[0] == true)
    {
        CloseBuyPosition();
        // DON'T reset flags - keep waiting for opposite signal
        // This ensures we wait for SELL signal after BUY position closes
    }
    //--- Close Sell when SuperTrend turns Green (bullish)
    else if(position_type == POSITION_TYPE_SELL && supertrend_trend[0] == false)
    {
        CloseSellPosition();
        // DON'T reset flags - keep waiting for opposite signal
        // This ensures we wait for BUY signal after SELL position closes
    }
}

//+------------------------------------------------------------------+
//| Open Buy Position                                                |
//+------------------------------------------------------------------+
bool OpenBuyPosition()
{
    double ask = Ask();
    double sl = (StopLoss > 0) ? ask - (StopLoss * _Point) : 0;
    double tp = (TakeProfit > 0) ? ask + (TakeProfit * _Point) : 0;

    if(trade.Buy(LotSize, _Symbol, ask, sl, tp, "SuperTrend Buy"))
    {
        Print("Buy order opened successfully at ", ask);

        //--- Store first position info for martingale
        if(UseMartingale && !martingale_active)
        {
            first_position_price = ask;
            first_position_sl = sl;
            first_position_ticket = trade.ResultOrder();
            martingale_triggered = false;
            Print("First position stored for distance martingale: Ticket=", first_position_ticket, " Price=", first_position_price);
        }

        return true;
    }
    else
    {
        Print("Failed to open buy order. Error: ", trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Open Sell Position                                               |
//+------------------------------------------------------------------+
bool OpenSellPosition()
{
    double bid = Bid();
    double sl = (StopLoss > 0) ? bid + (StopLoss * _Point) : 0;
    double tp = (TakeProfit > 0) ? bid - (TakeProfit * _Point) : 0;

    if(trade.Sell(LotSize, _Symbol, bid, sl, tp, "SuperTrend Sell"))
    {
        Print("Sell order opened successfully at ", bid);

        //--- Store first position info for martingale
        if(UseMartingale && !martingale_active)
        {
            first_position_price = bid;
            first_position_sl = sl;
            first_position_ticket = trade.ResultOrder();
            martingale_triggered = false;
            Print("First position stored for distance martingale: Ticket=", first_position_ticket, " Price=", first_position_price);
        }

        return true;
    }
    else
    {
        Print("Failed to open sell order. Error: ", trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close Buy Position                                               |
//+------------------------------------------------------------------+
void CloseBuyPosition()
{
    if(trade.PositionClose(_Symbol))
    {
        Print("Buy position closed successfully");
        // DON'T reset flags here - keep waiting for opposite signal
        // Flags will only reset when opposite signal actually executes
    }
    else
    {
        Print("Failed to close buy position. Error: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Close Sell Position                                              |
//+------------------------------------------------------------------+
void CloseSellPosition()
{
    if(trade.PositionClose(_Symbol))
    {
        Print("Sell position closed successfully");
        // DON'T reset flags here - keep waiting for opposite signal
        // Flags will only reset when opposite signal actually executes
    }
    else
    {
        Print("Failed to close sell position. Error: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Handle Trailing Stop                                             |
//+------------------------------------------------------------------+
void HandleTrailingStop()
{
    if(!PositionSelect(_Symbol))
        return;

    long position_type = PositionGetInteger(POSITION_TYPE);
    double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double position_sl = PositionGetDouble(POSITION_SL);
    double current_price = (position_type == POSITION_TYPE_BUY) ? Bid() : Ask();

    double trailing_distance = TrailingStop * _Point;
    double new_sl = 0;
    bool modify_needed = false;

    if(position_type == POSITION_TYPE_BUY)
    {
        //--- Calculate new stop loss for buy position
        new_sl = current_price - trailing_distance;

        //--- Check if we need to modify
        if(new_sl > position_sl + _Point || position_sl == 0)
        {
            modify_needed = true;
        }
    }
    else if(position_type == POSITION_TYPE_SELL)
    {
        //--- Calculate new stop loss for sell position
        new_sl = current_price + trailing_distance;

        //--- Check if we need to modify
        if(new_sl < position_sl - _Point || position_sl == 0)
        {
            modify_needed = true;
        }
    }

    //--- Modify position if needed
    if(modify_needed)
    {
        double tp = PositionGetDouble(POSITION_TP);
        if(trade.PositionModify(_Symbol, new_sl, tp))
        {
            Print("Trailing stop updated to ", new_sl);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if there are pending orders                               |
//+------------------------------------------------------------------+
bool HasPendingOrders()
{
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(OrderGetTicket(i)))
        {
            if(OrderGetString(ORDER_SYMBOL) == _Symbol &&
               OrderGetInteger(ORDER_MAGIC) == MAGIC_NUMBER)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get Ask price                                                    |
//+------------------------------------------------------------------+
double Ask()
{
    MqlTick tick;
    if(SymbolInfoTick(_Symbol, tick))
        return tick.ask;
    return 0;
}

//+------------------------------------------------------------------+
//| Get Bid price                                                    |
//+------------------------------------------------------------------+
double Bid()
{
    MqlTick tick;
    if(SymbolInfoTick(_Symbol, tick))
        return tick.bid;
    return 0;
}

//+------------------------------------------------------------------+
//| Check Distance-Based Martingale Conditions                      |
//+------------------------------------------------------------------+
void CheckMartingaleConditions()
{
    //--- Check if we have any positions
    if(!PositionSelect(_Symbol))
    {
        //--- Reset martingale flags if no positions
        ResetMartingaleFlags();
        return;
    }

    //--- If martingale already active, check for TP hit
    if(martingale_active)
    {
        CheckMartingaleTP();
        return;
    }

    //--- Check if we should trigger martingale based on distance
    if(!martingale_triggered && first_position_ticket > 0)
    {
        CheckDistanceTrigger();
    }
}

//+------------------------------------------------------------------+
//| Check if Distance should trigger Martingale                     |
//+------------------------------------------------------------------+
void CheckDistanceTrigger()
{
    double current_price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? Bid() : Ask();
    long position_type = PositionGetInteger(POSITION_TYPE);

    bool trigger_martingale = false;
    double distance_points = 0;

    if(position_type == POSITION_TYPE_BUY)
    {
        //--- For BUY: Check if price dropped by TriggerDistance points
        distance_points = (first_position_price - current_price) / _Point;
        if(distance_points >= TriggerDistance)
        {
            trigger_martingale = true;
            Print("BUY Distance trigger: ", distance_points, " points (target: ", TriggerDistance, ")");
        }
    }
    else if(position_type == POSITION_TYPE_SELL)
    {
        //--- For SELL: Check if price rose by TriggerDistance points
        distance_points = (current_price - first_position_price) / _Point;
        if(distance_points >= TriggerDistance)
        {
            trigger_martingale = true;
            Print("SELL Distance trigger: ", distance_points, " points (target: ", TriggerDistance, ")");
        }
    }

    if(trigger_martingale)
    {
        OpenMartingalePosition();
    }
}

//+------------------------------------------------------------------+
//| Open Distance-Based Martingale Position                         |
//+------------------------------------------------------------------+
void OpenMartingalePosition()
{
    long position_type = PositionGetInteger(POSITION_TYPE);
    double current_price = (position_type == POSITION_TYPE_BUY) ? Ask() : Bid();

    //--- Calculate martingale TP and final SL
    double martingale_tp, final_sl;

    if(position_type == POSITION_TYPE_BUY)
    {
        //--- BUY Martingale: TP above current price, SL below old SL
        martingale_tp = current_price + (MartingaleTP_Points * _Point);
        final_sl = first_position_sl - (FinalSL_Points * _Point);

        if(trade.Buy(MartingaleLotSize, _Symbol, current_price, final_sl, martingale_tp, "Distance Martingale Buy"))
        {
            martingale_ticket = trade.ResultOrder();
            martingale_active = true;
            martingale_triggered = true;

            //--- Modify first position SL to final SL
            if(PositionSelectByTicket(first_position_ticket))
            {
                double first_tp = PositionGetDouble(POSITION_TP);
                trade.PositionModify(_Symbol, final_sl, first_tp);
            }

            Print("Distance Martingale BUY opened: Lot=", MartingaleLotSize, " TP=", martingale_tp, " Final SL=", final_sl);
        }
    }
    else if(position_type == POSITION_TYPE_SELL)
    {
        //--- SELL Martingale: TP below current price, SL above old SL
        martingale_tp = current_price - (MartingaleTP_Points * _Point);
        final_sl = first_position_sl + (FinalSL_Points * _Point);

        if(trade.Sell(MartingaleLotSize, _Symbol, current_price, final_sl, martingale_tp, "Distance Martingale Sell"))
        {
            martingale_ticket = trade.ResultOrder();
            martingale_active = true;
            martingale_triggered = true;

            //--- Modify first position SL to final SL
            if(PositionSelectByTicket(first_position_ticket))
            {
                double first_tp = PositionGetDouble(POSITION_TP);
                trade.PositionModify(_Symbol, final_sl, first_tp);
            }

            Print("Distance Martingale SELL opened: Lot=", MartingaleLotSize, " TP=", martingale_tp, " Final SL=", final_sl);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if Martingale TP is hit and close all positions           |
//+------------------------------------------------------------------+
void CheckMartingaleTP()
{
    //--- Check if martingale position still exists
    if(!PositionSelectByTicket(martingale_ticket))
    {
        //--- Martingale position was closed - only close all if it was profitable
        //--- Check if any remaining positions are profitable before closing all
        bool has_profitable_position = false;
        for(int i = 0; i < PositionsTotal(); i++)
        {
            if(PositionGetSymbol(i) == _Symbol)
            {
                if(PositionGetDouble(POSITION_PROFIT) > 0)
                {
                    has_profitable_position = true;
                    break;
                }
            }
        }

        if(has_profitable_position)
        {
            Print("Martingale position closed with profit - closing all remaining positions");
            CloseAllPositions();
        }
        else
        {
            Print("Martingale position closed at loss - keeping remaining positions");
        }

        ResetMartingaleFlags();
        return;
    }

    //--- Check if martingale reached TP level (more precise check)
    if(PositionSelectByTicket(martingale_ticket))
    {
        double position_open = PositionGetDouble(POSITION_PRICE_OPEN);
        double position_tp = PositionGetDouble(POSITION_TP);
        long position_type = PositionGetInteger(POSITION_TYPE);
        double current_price = (position_type == POSITION_TYPE_BUY) ? Bid() : Ask();

        //--- Check if price reached TP level (with small buffer for spread)
        bool tp_reached = false;
        double buffer = 5 * _Point; // 5 point buffer

        if(position_type == POSITION_TYPE_BUY && current_price >= (position_tp - buffer))
            tp_reached = true;
        else if(position_type == POSITION_TYPE_SELL && current_price <= (position_tp + buffer))
            tp_reached = true;

        if(tp_reached)
        {
            Print("Martingale TP level reached at ", current_price, " (TP: ", position_tp, ") - closing all positions");
            CloseAllPositions();
            ResetMartingaleFlags();
        }
    }
}

//+------------------------------------------------------------------+
//| Close All Positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    //--- Close all positions for this symbol
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            ulong ticket = PositionGetTicket(i);
            if(ticket > 0)
            {
                trade.PositionClose(ticket);
                Print("Closed position ticket: ", ticket);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Reset Martingale Flags                                          |
//+------------------------------------------------------------------+
void ResetMartingaleFlags()
{
    martingale_active = false;
    first_position_price = 0;
    first_position_sl = 0;
    first_position_ticket = 0;
    martingale_ticket = 0;
    martingale_triggered = false;
    Print("Distance Martingale flags reset");
}
