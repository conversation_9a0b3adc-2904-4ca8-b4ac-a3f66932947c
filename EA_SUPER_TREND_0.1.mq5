//+------------------------------------------------------------------+
//|                                                SuperTrend_EA.mq5 |
//|                                  Copyright 2024, Augment Agent   |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Agent"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "SuperTrend Expert Advisor with trend reversal strategy"

//--- Include necessary libraries
#include <Trade\Trade.mqh>

//--- Input parameters
input group "=== SuperTrend Settings ==="
input int      ATR_Period = 10;           // ATR Period
input double   Multiplier = 3.0;          // SuperTrend Multiplier

input group "=== Strategy Combo Settings ==="
input bool     UseRSI = true;             // Use RSI Filter
input int      RSI_Period = 14;           // RSI Period
input double   RSI_Oversold = 30;         // RSI Oversold Level
input double   RSI_Overbought = 70;       // RSI Overbought Level
input bool     UseRSI_Divergence = true;  // Use RSI Divergence Detection

input bool     UseMACD = true;            // Use MACD Filter
input int      MACD_Fast = 12;            // MACD Fast EMA
input int      MACD_Slow = 26;            // MACD Slow EMA
input int      MACD_Signal = 9;           // MACD Signal Line

input bool     UseVolumeFilter = true;    // Use Volume Filter
input double   VolumeMultiplier = 1.5;    // Volume Spike Multiplier
input int      VolumeMA_Period = 20;      // Volume MA Period

input bool     UseSupportResistance = true; // Use Support/Resistance
input int      SR_Period = 20;            // S/R Lookback Period
input double   SR_Threshold = 50;         // S/R Distance Threshold (points)

input group "=== Trading Settings ==="
input double   LotSize = 0.1;             // Lot Size
input ENUM_TIMEFRAMES Timeframe = PERIOD_M15; // Trading Timeframe
input int      StopLoss = 2000;           // Stop Loss in points
input int      TakeProfit = 2000;         // Take Profit in points
input bool     UsePartialTP = true;       // Use Partial Take Profit
input double   PartialTP_Percent = 50;    // Partial TP Percentage
input double   PartialTP_Ratio = 1.5;     // Partial TP Risk:Reward Ratio

input group "=== Optional Settings ==="
input bool     UseTrailingStop = true;    // Use Trailing Stop
input int      TrailingStop = 250;        // Trailing Stop in points
input bool     UseATR_TrailingStop = true; // Use ATR-based Trailing Stop
input double   ATR_TrailingMultiplier = 2.0; // ATR Trailing Multiplier
input bool     UseTimeFilter = true;      // Use Time Filter
input int      StartHour = 8;             // Trading Start Hour
input int      EndHour = 17;              // Trading End Hour

input group "=== Risk Management ==="
input int      MaxSpread = 50;            // Maximum Spread in points
input int      Slippage = 10;             // Maximum Slippage in points
input bool     OneTradePerSignal = true;  // Allow only one trade per signal
input double   MaxDailyLoss = 5.0;        // Maximum Daily Loss (% of balance)
input bool     UseDynamicLotSize = true;  // Use Dynamic Lot Sizing
input double   RiskPerTrade = 2.0;        // Risk Per Trade (% of balance)

//--- Global variables
CTrade trade;
int atr_handle;
double atr_buffer[];
double high_buffer[], low_buffer[], close_buffer[];
double supertrend_upper[], supertrend_lower[];
bool supertrend_trend[];
int bars_total;
datetime last_bar_time;
datetime last_trade_time;        // Time of last trade to prevent multiple trades per bar
bool last_signal_processed;      // Flag to track if current signal was already processed
int last_trade_type;             // Track last trade type: 0=none, 1=buy, 2=sell
bool waiting_for_opposite_signal; // Flag to wait for opposite signal after trade

//--- Strategy Combo Indicators
int rsi_handle;
double rsi_buffer[];
int macd_handle;
double macd_main[], macd_signal[];
int volume_ma_handle;
double volume_buffer[], volume_ma_buffer[];
double support_levels[], resistance_levels[];

//--- Risk Management Variables
double daily_start_balance;
datetime daily_start_time;
bool partial_tp_executed;

//--- Magic number for orders
#define MAGIC_NUMBER 123456

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set trade parameters
    trade.SetExpertMagicNumber(MAGIC_NUMBER);
    trade.SetDeviationInPoints(Slippage);

    //--- Create ATR indicator handle
    atr_handle = iATR(_Symbol, Timeframe, ATR_Period);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator handle");
        return INIT_FAILED;
    }

    //--- Create Strategy Combo indicator handles
    if(UseRSI)
    {
        rsi_handle = iRSI(_Symbol, Timeframe, RSI_Period, PRICE_CLOSE);
        if(rsi_handle == INVALID_HANDLE)
        {
            Print("Failed to create RSI indicator handle");
            return INIT_FAILED;
        }
    }

    if(UseMACD)
    {
        macd_handle = iMACD(_Symbol, Timeframe, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
        if(macd_handle == INVALID_HANDLE)
        {
            Print("Failed to create MACD indicator handle");
            return INIT_FAILED;
        }
    }

    if(UseVolumeFilter)
    {
        volume_ma_handle = iMA(_Symbol, Timeframe, VolumeMA_Period, 0, MODE_SMA, VOLUME_TICK);
        if(volume_ma_handle == INVALID_HANDLE)
        {
            Print("Failed to create Volume MA indicator handle");
            return INIT_FAILED;
        }
    }

    //--- Initialize arrays
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(high_buffer, true);
    ArraySetAsSeries(low_buffer, true);
    ArraySetAsSeries(close_buffer, true);
    ArraySetAsSeries(supertrend_upper, true);
    ArraySetAsSeries(supertrend_lower, true);
    ArraySetAsSeries(supertrend_trend, true);

    //--- Initialize Strategy Combo arrays
    if(UseRSI)
        ArraySetAsSeries(rsi_buffer, true);
    if(UseMACD)
    {
        ArraySetAsSeries(macd_main, true);
        ArraySetAsSeries(macd_signal, true);
    }
    if(UseVolumeFilter)
    {
        ArraySetAsSeries(volume_buffer, true);
        ArraySetAsSeries(volume_ma_buffer, true);
    }
    if(UseSupportResistance)
    {
        ArrayResize(support_levels, SR_Period);
        ArrayResize(resistance_levels, SR_Period);
    }

    //--- Get initial bar count
    bars_total = iBars(_Symbol, Timeframe);
    last_bar_time = iTime(_Symbol, Timeframe, 0);

    //--- Initialize trade control variables
    last_trade_time = 0;
    last_signal_processed = false;
    last_trade_type = 0;              // 0=none, 1=buy, 2=sell
    waiting_for_opposite_signal = false;

    //--- Initialize risk management variables
    daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    daily_start_time = TimeCurrent();
    partial_tp_executed = false;

    Print("SuperTrend EA with Strategy Combo initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handles
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
    if(UseRSI && rsi_handle != INVALID_HANDLE)
        IndicatorRelease(rsi_handle);
    if(UseMACD && macd_handle != INVALID_HANDLE)
        IndicatorRelease(macd_handle);
    if(UseVolumeFilter && volume_ma_handle != INVALID_HANDLE)
        IndicatorRelease(volume_ma_handle);

    Print("SuperTrend EA with Strategy Combo deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check for new bar
    if(!IsNewBar())
        return;

    //--- Check daily loss limit
    if(!CheckDailyLossLimit())
        return;

    //--- Check time filter
    if(UseTimeFilter && !IsTimeToTrade())
        return;

    //--- Check spread
    if(GetSpread() > MaxSpread)
        return;

    //--- Calculate SuperTrend
    if(!CalculateSuperTrend())
        return;

    //--- Calculate Strategy Combo indicators
    if(!CalculateStrategyCombo())
        return;

    //--- Handle trailing stop
    if(UseTrailingStop)
        HandleTrailingStop();

    //--- Check for partial take profit
    if(UsePartialTP)
        CheckPartialTakeProfit();

    //--- Check for entry signals
    CheckEntrySignals();

    //--- Check for exit signals
    CheckExitSignals();
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime current_time = iTime(_Symbol, Timeframe, 0);
    if(current_time != last_bar_time)
    {
        last_bar_time = current_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    if(StartHour <= EndHour)
        return (dt.hour >= StartHour && dt.hour < EndHour);
    else
        return (dt.hour >= StartHour || dt.hour < EndHour);
}

//+------------------------------------------------------------------+
//| Get current spread in points                                     |
//+------------------------------------------------------------------+
int GetSpread()
{
    return (int)((Ask() - Bid()) / _Point);
}

//+------------------------------------------------------------------+
//| Calculate SuperTrend indicator                                   |
//+------------------------------------------------------------------+
bool CalculateSuperTrend()
{
    //--- Get required data
    if(CopyBuffer(atr_handle, 0, 0, 3, atr_buffer) < 3)
        return false;
    
    if(CopyHigh(_Symbol, Timeframe, 0, 3, high_buffer) < 3)
        return false;
    
    if(CopyLow(_Symbol, Timeframe, 0, 3, low_buffer) < 3)
        return false;
    
    if(CopyClose(_Symbol, Timeframe, 0, 3, close_buffer) < 3)
        return false;
    
    //--- Resize SuperTrend arrays
    ArrayResize(supertrend_upper, 3);
    ArrayResize(supertrend_lower, 3);
    ArrayResize(supertrend_trend, 3);
    
    //--- Calculate SuperTrend for each bar
    for(int i = 2; i >= 0; i--)
    {
        double hl2 = (high_buffer[i] + low_buffer[i]) / 2.0;
        double atr_value = atr_buffer[i];
        
        //--- Calculate basic upper and lower bands
        double basic_upper = hl2 + (Multiplier * atr_value);
        double basic_lower = hl2 - (Multiplier * atr_value);
        
        //--- Calculate final upper and lower bands
        if(i == 2) // First calculation
        {
            supertrend_upper[i] = basic_upper;
            supertrend_lower[i] = basic_lower;
        }
        else
        {
            supertrend_upper[i] = (basic_upper < supertrend_upper[i+1] || close_buffer[i+1] > supertrend_upper[i+1]) ? 
                                  basic_upper : supertrend_upper[i+1];
            
            supertrend_lower[i] = (basic_lower > supertrend_lower[i+1] || close_buffer[i+1] < supertrend_lower[i+1]) ? 
                                  basic_lower : supertrend_lower[i+1];
        }
        
        //--- Determine trend direction
        if(i == 2) // First calculation
        {
            supertrend_trend[i] = close_buffer[i] <= supertrend_lower[i];
        }
        else
        {
            if(supertrend_trend[i+1] && close_buffer[i] > supertrend_lower[i])
                supertrend_trend[i] = false; // Bullish
            else if(!supertrend_trend[i+1] && close_buffer[i] < supertrend_upper[i])
                supertrend_trend[i] = true;  // Bearish
            else
                supertrend_trend[i] = supertrend_trend[i+1]; // No change
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate Strategy Combo Indicators                              |
//+------------------------------------------------------------------+
bool CalculateStrategyCombo()
{
    //--- Get RSI data
    if(UseRSI)
    {
        if(CopyBuffer(rsi_handle, 0, 0, 3, rsi_buffer) < 3)
            return false;
    }

    //--- Get MACD data
    if(UseMACD)
    {
        if(CopyBuffer(macd_handle, 0, 0, 3, macd_main) < 3)
            return false;
        if(CopyBuffer(macd_handle, 1, 0, 3, macd_signal) < 3)
            return false;
    }

    //--- Get Volume data
    if(UseVolumeFilter)
    {
        if(CopyTickVolume(_Symbol, Timeframe, 0, 3, volume_buffer) < 3)
            return false;
        if(CopyBuffer(volume_ma_handle, 0, 0, 3, volume_ma_buffer) < 3)
            return false;
    }

    //--- Calculate Support/Resistance levels
    if(UseSupportResistance)
    {
        CalculateSupportResistance();
    }

    return true;
}

//+------------------------------------------------------------------+
//| Calculate Support and Resistance Levels                         |
//+------------------------------------------------------------------+
void CalculateSupportResistance()
{
    double highs[], lows[];
    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);

    if(CopyHigh(_Symbol, Timeframe, 1, SR_Period, highs) < SR_Period)
        return;
    if(CopyLow(_Symbol, Timeframe, 1, SR_Period, lows) < SR_Period)
        return;

    //--- Find resistance levels (recent highs)
    for(int i = 0; i < SR_Period; i++)
    {
        resistance_levels[i] = highs[i];
    }

    //--- Find support levels (recent lows)
    for(int i = 0; i < SR_Period; i++)
    {
        support_levels[i] = lows[i];
    }

    //--- Sort arrays
    ArraySort(resistance_levels, WHOLE_ARRAY, 0, MODE_DESCEND);
    ArraySort(support_levels, WHOLE_ARRAY, 0, MODE_ASCEND);
}

//+------------------------------------------------------------------+
//| Check Strategy Combo Signals                                    |
//+------------------------------------------------------------------+
bool CheckStrategyComboSignal(bool is_buy_signal)
{
    int confirmations = 0;
    int total_strategies = 0;

    //--- RSI Confirmation
    if(UseRSI)
    {
        total_strategies++;
        if(is_buy_signal)
        {
            // For buy: RSI should be oversold or showing bullish divergence
            if(rsi_buffer[0] < RSI_Oversold ||
               (UseRSI_Divergence && CheckRSIDivergence(true)))
                confirmations++;
        }
        else
        {
            // For sell: RSI should be overbought or showing bearish divergence
            if(rsi_buffer[0] > RSI_Overbought ||
               (UseRSI_Divergence && CheckRSIDivergence(false)))
                confirmations++;
        }
    }

    //--- MACD Confirmation
    if(UseMACD)
    {
        total_strategies++;
        if(is_buy_signal)
        {
            // For buy: MACD line above signal line or crossing above
            if(macd_main[0] > macd_signal[0] ||
               (macd_main[0] > macd_signal[0] && macd_main[1] <= macd_signal[1]))
                confirmations++;
        }
        else
        {
            // For sell: MACD line below signal line or crossing below
            if(macd_main[0] < macd_signal[0] ||
               (macd_main[0] < macd_signal[0] && macd_main[1] >= macd_signal[1]))
                confirmations++;
        }
    }

    //--- Volume Confirmation
    if(UseVolumeFilter)
    {
        total_strategies++;
        // Volume should be above average for strong signals
        if(volume_buffer[0] > volume_ma_buffer[0] * VolumeMultiplier)
            confirmations++;
    }

    //--- Support/Resistance Confirmation
    if(UseSupportResistance)
    {
        total_strategies++;
        double current_price = (high_buffer[0] + low_buffer[0]) / 2.0;

        if(is_buy_signal)
        {
            // For buy: price should be near support
            for(int i = 0; i < ArraySize(support_levels); i++)
            {
                if(MathAbs(current_price - support_levels[i]) <= SR_Threshold * _Point)
                {
                    confirmations++;
                    break;
                }
            }
        }
        else
        {
            // For sell: price should be near resistance
            for(int i = 0; i < ArraySize(resistance_levels); i++)
            {
                if(MathAbs(current_price - resistance_levels[i]) <= SR_Threshold * _Point)
                {
                    confirmations++;
                    break;
                }
            }
        }
    }

    //--- Require at least 60% of strategies to confirm
    double confirmation_ratio = (double)confirmations / total_strategies;
    return confirmation_ratio >= 0.6;
}

//+------------------------------------------------------------------+
//| Check RSI Divergence                                            |
//+------------------------------------------------------------------+
bool CheckRSIDivergence(bool bullish)
{
    if(ArraySize(rsi_buffer) < 3 || ArraySize(close_buffer) < 3)
        return false;

    if(bullish)
    {
        // Bullish divergence: price makes lower low, RSI makes higher low
        return (close_buffer[0] < close_buffer[2] && rsi_buffer[0] > rsi_buffer[2]);
    }
    else
    {
        // Bearish divergence: price makes higher high, RSI makes lower high
        return (close_buffer[0] > close_buffer[2] && rsi_buffer[0] < rsi_buffer[2]);
    }
}

//+------------------------------------------------------------------+
//| Check for entry signals                                          |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
    //--- Check if we already have a position
    if(PositionSelect(_Symbol))
        return;

    //--- Check for pending orders
    if(HasPendingOrders())
        return;

    //--- Check for trend change signals
    if(ArraySize(supertrend_trend) < 2)
        return;

    //--- Detect SuperTrend signals
    bool current_signal_buy = (supertrend_trend[1] == true && supertrend_trend[0] == false);
    bool current_signal_sell = (supertrend_trend[1] == false && supertrend_trend[0] == true);

    //--- Apply Strategy Combo Filter
    if(current_signal_buy && !CheckStrategyComboSignal(true))
    {
        Print("Buy signal filtered out by Strategy Combo");
        return;
    }

    if(current_signal_sell && !CheckStrategyComboSignal(false))
    {
        Print("Sell signal filtered out by Strategy Combo");
        return;
    }

    //--- Prevent multiple trades on the same bar
    datetime current_bar_time = iTime(_Symbol, Timeframe, 0);
    if(OneTradePerSignal && last_trade_time == current_bar_time)
        return;

    //--- OneTradePerSignal Logic: Wait for opposite signal
    if(OneTradePerSignal && waiting_for_opposite_signal)
    {
        // If last trade was BUY (1), only allow SELL signals
        if(last_trade_type == 1 && !current_signal_sell)
            return;

        // If last trade was SELL (2), only allow BUY signals
        if(last_trade_type == 2 && !current_signal_buy)
            return;
    }

    //--- Buy signal: SuperTrend changes from Red to Green (bearish to bullish) + Strategy Combo confirmation
    if(current_signal_buy)
    {
        if(OpenBuyPosition())
        {
            if(OneTradePerSignal)
            {
                last_trade_time = current_bar_time;
                // Reset flags from previous trade and set new state
                waiting_for_opposite_signal = true;
                last_trade_type = 1;  // Remember this was a BUY trade - now wait for SELL
            }
            Print("COMBO Buy signal processed at ", TimeToString(current_bar_time));
        }
    }
    //--- Sell signal: SuperTrend changes from Green to Red (bullish to bearish) + Strategy Combo confirmation
    else if(current_signal_sell)
    {
        if(OpenSellPosition())
        {
            if(OneTradePerSignal)
            {
                last_trade_time = current_bar_time;
                // Reset flags from previous trade and set new state
                waiting_for_opposite_signal = true;
                last_trade_type = 2;  // Remember this was a SELL trade - now wait for BUY
            }
            Print("COMBO Sell signal processed at ", TimeToString(current_bar_time));
        }
    }
}

//+------------------------------------------------------------------+
//| Check for exit signals                                           |
//+------------------------------------------------------------------+
void CheckExitSignals()
{
    //--- Check if we have a position
    if(!PositionSelect(_Symbol))
        return;

    long position_type = PositionGetInteger(POSITION_TYPE);

    //--- Close Buy when SuperTrend turns Red (bearish)
    if(position_type == POSITION_TYPE_BUY && supertrend_trend[0] == true)
    {
        CloseBuyPosition();
        // DON'T reset flags - keep waiting for opposite signal
        // This ensures we wait for SELL signal after BUY position closes
    }
    //--- Close Sell when SuperTrend turns Green (bullish)
    else if(position_type == POSITION_TYPE_SELL && supertrend_trend[0] == false)
    {
        CloseSellPosition();
        // DON'T reset flags - keep waiting for opposite signal
        // This ensures we wait for BUY signal after SELL position closes
    }
}

//+------------------------------------------------------------------+
//| Open Buy Position                                                |
//+------------------------------------------------------------------+
bool OpenBuyPosition()
{
    double ask = Ask();
    double lot_size = CalculateLotSize();
    double sl = (StopLoss > 0) ? ask - (StopLoss * _Point) : 0;
    double tp = (TakeProfit > 0) ? ask + (TakeProfit * _Point) : 0;

    if(trade.Buy(lot_size, _Symbol, ask, sl, tp, "SuperTrend Combo Buy"))
    {
        Print("Buy order opened successfully at ", ask, " with lot size ", lot_size);
        partial_tp_executed = false; // Reset partial TP flag
        return true;
    }
    else
    {
        Print("Failed to open buy order. Error: ", trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Open Sell Position                                               |
//+------------------------------------------------------------------+
bool OpenSellPosition()
{
    double bid = Bid();
    double lot_size = CalculateLotSize();
    double sl = (StopLoss > 0) ? bid + (StopLoss * _Point) : 0;
    double tp = (TakeProfit > 0) ? bid - (TakeProfit * _Point) : 0;

    if(trade.Sell(lot_size, _Symbol, bid, sl, tp, "SuperTrend Combo Sell"))
    {
        Print("Sell order opened successfully at ", bid, " with lot size ", lot_size);
        partial_tp_executed = false; // Reset partial TP flag
        return true;
    }
    else
    {
        Print("Failed to open sell order. Error: ", trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close Buy Position                                               |
//+------------------------------------------------------------------+
void CloseBuyPosition()
{
    if(trade.PositionClose(_Symbol))
    {
        Print("Buy position closed successfully");
        // DON'T reset flags here - keep waiting for opposite signal
        // Flags will only reset when opposite signal actually executes
    }
    else
    {
        Print("Failed to close buy position. Error: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Close Sell Position                                              |
//+------------------------------------------------------------------+
void CloseSellPosition()
{
    if(trade.PositionClose(_Symbol))
    {
        Print("Sell position closed successfully");
        // DON'T reset flags here - keep waiting for opposite signal
        // Flags will only reset when opposite signal actually executes
    }
    else
    {
        Print("Failed to close sell position. Error: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Handle Trailing Stop                                             |
//+------------------------------------------------------------------+
void HandleTrailingStop()
{
    if(!PositionSelect(_Symbol))
        return;

    long position_type = PositionGetInteger(POSITION_TYPE);
    double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double position_sl = PositionGetDouble(POSITION_SL);
    double current_price = (position_type == POSITION_TYPE_BUY) ? Bid() : Ask();

    double trailing_distance;

    //--- Use ATR-based trailing stop if enabled
    if(UseATR_TrailingStop && ArraySize(atr_buffer) > 0)
    {
        trailing_distance = atr_buffer[0] * ATR_TrailingMultiplier;
    }
    else
    {
        trailing_distance = TrailingStop * _Point;
    }

    double new_sl = 0;
    bool modify_needed = false;

    if(position_type == POSITION_TYPE_BUY)
    {
        //--- Calculate new stop loss for buy position
        new_sl = current_price - trailing_distance;

        //--- Check if we need to modify (only move SL up for buy)
        if(new_sl > position_sl + _Point || position_sl == 0)
        {
            modify_needed = true;
        }
    }
    else if(position_type == POSITION_TYPE_SELL)
    {
        //--- Calculate new stop loss for sell position
        new_sl = current_price + trailing_distance;

        //--- Check if we need to modify (only move SL down for sell)
        if(new_sl < position_sl - _Point || position_sl == 0)
        {
            modify_needed = true;
        }
    }

    //--- Modify position if needed
    if(modify_needed)
    {
        double tp = PositionGetDouble(POSITION_TP);
        if(trade.PositionModify(_Symbol, new_sl, tp))
        {
            string trail_type = UseATR_TrailingStop ? "ATR-based" : "Fixed";
            Print(trail_type, " trailing stop updated to ", new_sl, " (distance: ", trailing_distance/_Point, " points)");
        }
    }
}

//+------------------------------------------------------------------+
//| Check if there are pending orders                               |
//+------------------------------------------------------------------+
bool HasPendingOrders()
{
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(OrderGetTicket(i)))
        {
            if(OrderGetString(ORDER_SYMBOL) == _Symbol &&
               OrderGetInteger(ORDER_MAGIC) == MAGIC_NUMBER)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get Ask price                                                    |
//+------------------------------------------------------------------+
double Ask()
{
    MqlTick tick;
    if(SymbolInfoTick(_Symbol, tick))
        return tick.ask;
    return 0;
}

//+------------------------------------------------------------------+
//| Get Bid price                                                    |
//+------------------------------------------------------------------+
double Bid()
{
    MqlTick tick;
    if(SymbolInfoTick(_Symbol, tick))
        return tick.bid;
    return 0;
}

//+------------------------------------------------------------------+
//| Calculate Dynamic Lot Size                                      |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    if(!UseDynamicLotSize)
        return LotSize;

    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * RiskPerTrade / 100.0;
    double stop_loss_points = StopLoss;

    if(stop_loss_points <= 0)
        return LotSize;

    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lot_size = risk_amount / (stop_loss_points * tick_value);

    //--- Apply lot size limits
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(lot_size, min_lot);
    lot_size = MathMin(lot_size, max_lot);
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Check Daily Loss Limit                                          |
//+------------------------------------------------------------------+
bool CheckDailyLossLimit()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    //--- Reset daily tracking at start of new day
    MqlDateTime start_dt;
    TimeToStruct(daily_start_time, start_dt);

    if(dt.day != start_dt.day)
    {
        daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        daily_start_time = TimeCurrent();
        return true;
    }

    //--- Check current loss
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double daily_loss_percent = (daily_start_balance - current_balance) / daily_start_balance * 100.0;

    if(daily_loss_percent >= MaxDailyLoss)
    {
        Print("Daily loss limit reached: ", daily_loss_percent, "%");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check Partial Take Profit                                       |
//+------------------------------------------------------------------+
void CheckPartialTakeProfit()
{
    if(!PositionSelect(_Symbol) || partial_tp_executed)
        return;

    double position_profit = PositionGetDouble(POSITION_PROFIT);
    double position_volume = PositionGetDouble(POSITION_VOLUME);
    double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    long position_type = PositionGetInteger(POSITION_TYPE);

    //--- Calculate target profit for partial TP
    double risk_amount = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPerTrade / 100.0;
    double target_profit = risk_amount * PartialTP_Ratio;

    if(position_profit >= target_profit)
    {
        //--- Close partial position
        double partial_volume = position_volume * PartialTP_Percent / 100.0;
        partial_volume = NormalizeDouble(partial_volume, 2);

        if(trade.PositionClosePartial(_Symbol, partial_volume))
        {
            Print("Partial take profit executed: ", partial_volume, " lots at profit: ", position_profit);
            partial_tp_executed = true;

            //--- Move remaining position to breakeven
            if(PositionSelect(_Symbol))
            {
                double current_sl = PositionGetDouble(POSITION_SL);
                double current_tp = PositionGetDouble(POSITION_TP);
                double breakeven_price = position_open_price;

                if(position_type == POSITION_TYPE_BUY)
                    breakeven_price += 10 * _Point; // Small buffer
                else
                    breakeven_price -= 10 * _Point; // Small buffer

                trade.PositionModify(_Symbol, breakeven_price, current_tp);
                Print("Position moved to breakeven");
            }
        }
    }
}
