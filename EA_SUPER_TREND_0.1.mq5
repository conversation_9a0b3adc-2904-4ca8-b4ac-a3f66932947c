//+------------------------------------------------------------------+
//|                                                SuperTrend_EA.mq5 |
//|                                  Copyright 2024, Augment Agent   |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Agent"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "SuperTrend Expert Advisor with trend reversal strategy"

//--- Include necessary libraries
#include <Trade\Trade.mqh>

//--- Input parameters
input group "=== SuperTrend Settings ==="
input int      ATR_Period = 10;           // ATR Period
input double   Multiplier = 3.0;          // SuperTrend Multiplier

input group "=== Trading Settings ==="
input double   LotSize = 0.1;             // Lot Size
input ENUM_TIMEFRAMES Timeframe = PERIOD_M15; // Trading Timeframe
input int      StopLoss = 2000;           // Stop Loss in points
input int      TakeProfit = 2000;         // Take Profit in points

input group "=== Optional Settings ==="
input bool     UseTrailingStop = true;    // Use Trailing Stop
input int      TrailingStop = 250;        // Trailing Stop in points
input bool     UseTimeFilter = true;      // Use Time Filter
input int      StartHour = 8;             // Trading Start Hour
input int      EndHour = 17;              // Trading End Hour

input group "=== Bollinger Bands Strategy ==="
input bool     UseBollingerBands = true;  // Use Bollinger Bands Confirmation
input int      BB_Period = 20;            // Bollinger Bands Period
input double   BB_Deviation = 2.0;        // Bollinger Bands Deviation
input bool     RequireBB_Breakout = true; // Require breakout from BB for entry
input bool     RequireBB_Squeeze = false; // Require BB squeeze before entry
input double   BB_SqueezeThreshold = 0.002; // BB squeeze threshold (band width)
input bool     UseBB_Rejection = true;    // Use BB rejection signals

input group "=== Martingale Settings ==="
input bool     UseMartingale = true;      // Use Martingale Position Management
input double   MartingaleLotSize = 0.03;  // Martingale Lot Size (larger than base)
input int      TriggerDistance = 15000;   // Distance from entry to trigger martingale (points)
input int      MartingaleTP_Points = 10000; // Martingale TP target (points)
input int      FinalSL_Points = 5000;     // Final SL from old SL (points)
input bool     DisableSuperTrendExit = true; // Disable SuperTrend exit signals when using martingale

input group "=== Risk Management ==="
input int      MaxSpread = 50;            // Maximum Spread in points
input int      Slippage = 10;             // Maximum Slippage in points
input bool     OneTradePerSignal = true;  // Allow only one trade per signal

//--- Global variables
CTrade trade;
int atr_handle;
double atr_buffer[];
double high_buffer[], low_buffer[], close_buffer[];
double supertrend_upper[], supertrend_lower[];
bool supertrend_trend[];
int bars_total;
datetime last_bar_time;
datetime last_trade_time;        // Time of last trade to prevent multiple trades per bar
bool last_signal_processed;      // Flag to track if current signal was already processed
int last_trade_type;             // Track last trade type: 0=none, 1=buy, 2=sell
bool waiting_for_opposite_signal; // Flag to wait for opposite signal after trade

//--- Bollinger Bands Variables
int bb_handle;
double bb_upper[], bb_middle[], bb_lower[];
bool bb_squeeze_detected;
datetime last_squeeze_time;

//--- Martingale Variables
bool martingale_active;           // Flag if martingale position is active
double first_position_price;     // Entry price of first position
double first_position_sl;        // Original SL of first position
ulong first_position_ticket;     // Ticket of first position
ulong martingale_ticket;         // Ticket of martingale position
bool martingale_triggered;       // Flag if martingale was already triggered

//--- Magic number for orders
#define MAGIC_NUMBER 123456

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set trade parameters
    trade.SetExpertMagicNumber(MAGIC_NUMBER);
    trade.SetDeviationInPoints(Slippage);
    
    //--- Create ATR indicator handle
    atr_handle = iATR(_Symbol, Timeframe, ATR_Period);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator handle");
        return INIT_FAILED;
    }

    //--- Create Bollinger Bands indicator handle
    if(UseBollingerBands)
    {
        bb_handle = iBands(_Symbol, Timeframe, BB_Period, 0, BB_Deviation, PRICE_CLOSE);
        if(bb_handle == INVALID_HANDLE)
        {
            Print("Failed to create Bollinger Bands indicator handle");
            return INIT_FAILED;
        }
    }

    //--- Initialize arrays
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(high_buffer, true);
    ArraySetAsSeries(low_buffer, true);
    ArraySetAsSeries(close_buffer, true);
    ArraySetAsSeries(supertrend_upper, true);
    ArraySetAsSeries(supertrend_lower, true);
    ArraySetAsSeries(supertrend_trend, true);

    //--- Initialize Bollinger Bands arrays
    if(UseBollingerBands)
    {
        ArraySetAsSeries(bb_upper, true);
        ArraySetAsSeries(bb_middle, true);
        ArraySetAsSeries(bb_lower, true);
    }
    
    //--- Get initial bar count
    bars_total = iBars(_Symbol, Timeframe);
    last_bar_time = iTime(_Symbol, Timeframe, 0);

    //--- Initialize trade control variables
    last_trade_time = 0;
    last_signal_processed = false;
    last_trade_type = 0;              // 0=none, 1=buy, 2=sell
    waiting_for_opposite_signal = false;

    //--- Initialize martingale variables
    martingale_active = false;
    first_position_price = 0;
    first_position_sl = 0;
    first_position_ticket = 0;
    martingale_ticket = 0;
    martingale_triggered = false;

    //--- Initialize Bollinger Bands variables
    bb_squeeze_detected = false;
    last_squeeze_time = 0;

    Print("SuperTrend EA with Bollinger Bands + Distance Martingale initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handles
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
    if(UseBollingerBands && bb_handle != INVALID_HANDLE)
        IndicatorRelease(bb_handle);

    Print("SuperTrend EA with Bollinger Bands deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check for new bar
    if(!IsNewBar())
        return;
    
    //--- Check time filter
    if(UseTimeFilter && !IsTimeToTrade())
        return;
    
    //--- Check spread
    if(GetSpread() > MaxSpread)
        return;
    
    //--- Calculate SuperTrend
    if(!CalculateSuperTrend())
        return;

    //--- Calculate Bollinger Bands
    if(UseBollingerBands && !CalculateBollingerBands())
        return;

    //--- Handle trailing stop
    if(UseTrailingStop)
        HandleTrailingStop();

    //--- Check martingale conditions
    if(UseMartingale)
        CheckMartingaleConditions();

    //--- Check for entry signals
    CheckEntrySignals();

    //--- Check for exit signals
    CheckExitSignals();
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime current_time = iTime(_Symbol, Timeframe, 0);
    if(current_time != last_bar_time)
    {
        last_bar_time = current_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    if(StartHour <= EndHour)
        return (dt.hour >= StartHour && dt.hour < EndHour);
    else
        return (dt.hour >= StartHour || dt.hour < EndHour);
}

//+------------------------------------------------------------------+
//| Get current spread in points                                     |
//+------------------------------------------------------------------+
int GetSpread()
{
    return (int)((Ask() - Bid()) / _Point);
}

//+------------------------------------------------------------------+
//| Calculate SuperTrend indicator                                   |
//+------------------------------------------------------------------+
bool CalculateSuperTrend()
{
    //--- Get required data
    if(CopyBuffer(atr_handle, 0, 0, 3, atr_buffer) < 3)
        return false;
    
    if(CopyHigh(_Symbol, Timeframe, 0, 3, high_buffer) < 3)
        return false;
    
    if(CopyLow(_Symbol, Timeframe, 0, 3, low_buffer) < 3)
        return false;
    
    if(CopyClose(_Symbol, Timeframe, 0, 3, close_buffer) < 3)
        return false;
    
    //--- Resize SuperTrend arrays
    ArrayResize(supertrend_upper, 3);
    ArrayResize(supertrend_lower, 3);
    ArrayResize(supertrend_trend, 3);
    
    //--- Calculate SuperTrend for each bar
    for(int i = 2; i >= 0; i--)
    {
        double hl2 = (high_buffer[i] + low_buffer[i]) / 2.0;
        double atr_value = atr_buffer[i];
        
        //--- Calculate basic upper and lower bands
        double basic_upper = hl2 + (Multiplier * atr_value);
        double basic_lower = hl2 - (Multiplier * atr_value);
        
        //--- Calculate final upper and lower bands
        if(i == 2) // First calculation
        {
            supertrend_upper[i] = basic_upper;
            supertrend_lower[i] = basic_lower;
        }
        else
        {
            supertrend_upper[i] = (basic_upper < supertrend_upper[i+1] || close_buffer[i+1] > supertrend_upper[i+1]) ? 
                                  basic_upper : supertrend_upper[i+1];
            
            supertrend_lower[i] = (basic_lower > supertrend_lower[i+1] || close_buffer[i+1] < supertrend_lower[i+1]) ? 
                                  basic_lower : supertrend_lower[i+1];
        }
        
        //--- Determine trend direction
        if(i == 2) // First calculation
        {
            supertrend_trend[i] = close_buffer[i] <= supertrend_lower[i];
        }
        else
        {
            if(supertrend_trend[i+1] && close_buffer[i] > supertrend_lower[i])
                supertrend_trend[i] = false; // Bullish
            else if(!supertrend_trend[i+1] && close_buffer[i] < supertrend_upper[i])
                supertrend_trend[i] = true;  // Bearish
            else
                supertrend_trend[i] = supertrend_trend[i+1]; // No change
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate Bollinger Bands                                       |
//+------------------------------------------------------------------+
bool CalculateBollingerBands()
{
    //--- Get Bollinger Bands data
    if(CopyBuffer(bb_handle, 0, 0, 3, bb_middle) < 3)
        return false;
    if(CopyBuffer(bb_handle, 1, 0, 3, bb_upper) < 3)
        return false;
    if(CopyBuffer(bb_handle, 2, 0, 3, bb_lower) < 3)
        return false;

    //--- Check for BB squeeze
    if(RequireBB_Squeeze)
        CheckBBSqueeze();

    return true;
}

//+------------------------------------------------------------------+
//| Check for Bollinger Bands Squeeze                               |
//+------------------------------------------------------------------+
void CheckBBSqueeze()
{
    //--- Calculate band width (normalized)
    double band_width = (bb_upper[0] - bb_lower[0]) / bb_middle[0];

    //--- Check if squeeze is detected
    if(band_width <= BB_SqueezeThreshold)
    {
        if(!bb_squeeze_detected)
        {
            bb_squeeze_detected = true;
            last_squeeze_time = iTime(_Symbol, Timeframe, 0);
            Print("Bollinger Bands squeeze detected at ", TimeToString(last_squeeze_time));
        }
    }
    else
    {
        //--- Squeeze ended - potential breakout
        if(bb_squeeze_detected)
        {
            bb_squeeze_detected = false;
            Print("Bollinger Bands squeeze ended - potential breakout");
        }
    }
}

//+------------------------------------------------------------------+
//| Check Bollinger Bands Signal Confirmation                       |
//+------------------------------------------------------------------+
bool CheckBollingerBandsSignal(bool is_buy_signal)
{
    if(!UseBollingerBands)
        return true; // If BB disabled, always confirm

    double current_close = close_buffer[0];
    double prev_close = close_buffer[1];
    double current_high = high_buffer[0];
    double current_low = low_buffer[0];

    bool bb_confirmed = false;

    if(is_buy_signal)
    {
        //--- BUY Signal Confirmations
        if(RequireBB_Breakout)
        {
            //--- Price should break above lower band or be in lower half moving up
            if(current_close > bb_lower[0] && prev_close <= bb_lower[1])
            {
                bb_confirmed = true;
                Print("BB BUY: Breakout above lower band confirmed");
            }
            else if(current_close < bb_middle[0] && current_close > prev_close)
            {
                bb_confirmed = true;
                Print("BB BUY: Price in lower half moving up");
            }
        }

        if(UseBB_Rejection)
        {
            //--- Check for rejection at lower band (bullish reversal)
            if(current_low <= bb_lower[0] && current_close > bb_lower[0])
            {
                bb_confirmed = true;
                Print("BB BUY: Rejection at lower band detected");
            }
        }

        if(RequireBB_Squeeze)
        {
            //--- Require recent squeeze for stronger signal
            datetime current_time = iTime(_Symbol, Timeframe, 0);
            if(current_time - last_squeeze_time <= 10 * PeriodSeconds(Timeframe))
            {
                bb_confirmed = true;
                Print("BB BUY: Recent squeeze breakout");
            }
        }

        //--- If no specific requirements, check general bullish condition
        if(!RequireBB_Breakout && !UseBB_Rejection && !RequireBB_Squeeze)
        {
            if(current_close > bb_middle[0] ||
               (current_close > bb_lower[0] && current_close > prev_close))
            {
                bb_confirmed = true;
                Print("BB BUY: General bullish condition");
            }
        }
    }
    else // SELL signal
    {
        //--- SELL Signal Confirmations
        if(RequireBB_Breakout)
        {
            //--- Price should break below upper band or be in upper half moving down
            if(current_close < bb_upper[0] && prev_close >= bb_upper[1])
            {
                bb_confirmed = true;
                Print("BB SELL: Breakout below upper band confirmed");
            }
            else if(current_close > bb_middle[0] && current_close < prev_close)
            {
                bb_confirmed = true;
                Print("BB SELL: Price in upper half moving down");
            }
        }

        if(UseBB_Rejection)
        {
            //--- Check for rejection at upper band (bearish reversal)
            if(current_high >= bb_upper[0] && current_close < bb_upper[0])
            {
                bb_confirmed = true;
                Print("BB SELL: Rejection at upper band detected");
            }
        }

        if(RequireBB_Squeeze)
        {
            //--- Require recent squeeze for stronger signal
            datetime current_time = iTime(_Symbol, Timeframe, 0);
            if(current_time - last_squeeze_time <= 10 * PeriodSeconds(Timeframe))
            {
                bb_confirmed = true;
                Print("BB SELL: Recent squeeze breakout");
            }
        }

        //--- If no specific requirements, check general bearish condition
        if(!RequireBB_Breakout && !UseBB_Rejection && !RequireBB_Squeeze)
        {
            if(current_close < bb_middle[0] ||
               (current_close < bb_upper[0] && current_close < prev_close))
            {
                bb_confirmed = true;
                Print("BB SELL: General bearish condition");
            }
        }
    }

    return bb_confirmed;
}

//+------------------------------------------------------------------+
//| Check for entry signals                                          |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
    //--- Check if we already have a position
    if(PositionSelect(_Symbol))
        return;

    //--- Check for pending orders
    if(HasPendingOrders())
        return;

    //--- Check for trend change signals
    if(ArraySize(supertrend_trend) < 2)
        return;

    //--- Detect SuperTrend signals
    bool current_signal_buy = (supertrend_trend[1] == true && supertrend_trend[0] == false);
    bool current_signal_sell = (supertrend_trend[1] == false && supertrend_trend[0] == true);

    //--- Apply Bollinger Bands Filter
    if(current_signal_buy && !CheckBollingerBandsSignal(true))
    {
        Print("Buy signal filtered out by Bollinger Bands");
        return;
    }

    if(current_signal_sell && !CheckBollingerBandsSignal(false))
    {
        Print("Sell signal filtered out by Bollinger Bands");
        return;
    }

    //--- Prevent multiple trades on the same bar
    datetime current_bar_time = iTime(_Symbol, Timeframe, 0);
    if(OneTradePerSignal && last_trade_time == current_bar_time)
        return;

    //--- OneTradePerSignal Logic: Wait for opposite signal
    if(OneTradePerSignal && waiting_for_opposite_signal)
    {
        // If last trade was BUY (1), only allow SELL signals
        if(last_trade_type == 1 && !current_signal_sell)
            return;

        // If last trade was SELL (2), only allow BUY signals
        if(last_trade_type == 2 && !current_signal_buy)
            return;
    }

    //--- Buy signal: SuperTrend + Bollinger Bands confirmation
    if(current_signal_buy)
    {
        if(OpenBuyPosition())
        {
            if(OneTradePerSignal)
            {
                last_trade_time = current_bar_time;
                // Reset flags from previous trade and set new state
                waiting_for_opposite_signal = true;
                last_trade_type = 1;  // Remember this was a BUY trade - now wait for SELL
            }
            Print("BB-CONFIRMED Buy signal processed at ", TimeToString(current_bar_time));
        }
    }
    //--- Sell signal: SuperTrend + Bollinger Bands confirmation
    else if(current_signal_sell)
    {
        if(OpenSellPosition())
        {
            if(OneTradePerSignal)
            {
                last_trade_time = current_bar_time;
                // Reset flags from previous trade and set new state
                waiting_for_opposite_signal = true;
                last_trade_type = 2;  // Remember this was a SELL trade - now wait for BUY
            }
            Print("BB-CONFIRMED Sell signal processed at ", TimeToString(current_bar_time));
        }
    }
}

//+------------------------------------------------------------------+
//| Check for exit signals                                           |
//+------------------------------------------------------------------+
void CheckExitSignals()
{
    //--- Exit signals disabled when using martingale (if setting enabled)
    //--- Martingale system will handle all exits
    if(UseMartingale && DisableSuperTrendExit)
        return;

    //--- Check if we have a position
    if(!PositionSelect(_Symbol))
        return;

    long position_type = PositionGetInteger(POSITION_TYPE);

    //--- Close Buy when SuperTrend turns Red (bearish)
    if(position_type == POSITION_TYPE_BUY && supertrend_trend[0] == true)
    {
        CloseBuyPosition();
        // DON'T reset flags - keep waiting for opposite signal
        // This ensures we wait for SELL signal after BUY position closes
    }
    //--- Close Sell when SuperTrend turns Green (bullish)
    else if(position_type == POSITION_TYPE_SELL && supertrend_trend[0] == false)
    {
        CloseSellPosition();
        // DON'T reset flags - keep waiting for opposite signal
        // This ensures we wait for BUY signal after SELL position closes
    }
}

//+------------------------------------------------------------------+
//| Open Buy Position                                                |
//+------------------------------------------------------------------+
bool OpenBuyPosition()
{
    double ask = Ask();
    double sl = (StopLoss > 0) ? ask - (StopLoss * _Point) : 0;
    double tp = (TakeProfit > 0) ? ask + (TakeProfit * _Point) : 0;

    if(trade.Buy(LotSize, _Symbol, ask, sl, tp, "SuperTrend Buy"))
    {
        Print("Buy order opened successfully at ", ask);

        //--- Store first position info for martingale
        if(UseMartingale && !martingale_active)
        {
            first_position_price = ask;
            first_position_sl = sl;
            first_position_ticket = trade.ResultOrder();
            martingale_triggered = false;
            Print("First position stored for distance martingale: Ticket=", first_position_ticket, " Price=", first_position_price);
        }

        return true;
    }
    else
    {
        Print("Failed to open buy order. Error: ", trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Open Sell Position                                               |
//+------------------------------------------------------------------+
bool OpenSellPosition()
{
    double bid = Bid();
    double sl = (StopLoss > 0) ? bid + (StopLoss * _Point) : 0;
    double tp = (TakeProfit > 0) ? bid - (TakeProfit * _Point) : 0;

    if(trade.Sell(LotSize, _Symbol, bid, sl, tp, "SuperTrend Sell"))
    {
        Print("Sell order opened successfully at ", bid);

        //--- Store first position info for martingale
        if(UseMartingale && !martingale_active)
        {
            first_position_price = bid;
            first_position_sl = sl;
            first_position_ticket = trade.ResultOrder();
            martingale_triggered = false;
            Print("First position stored for distance martingale: Ticket=", first_position_ticket, " Price=", first_position_price);
        }

        return true;
    }
    else
    {
        Print("Failed to open sell order. Error: ", trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close Buy Position                                               |
//+------------------------------------------------------------------+
void CloseBuyPosition()
{
    if(trade.PositionClose(_Symbol))
    {
        Print("Buy position closed successfully");
        // DON'T reset flags here - keep waiting for opposite signal
        // Flags will only reset when opposite signal actually executes
    }
    else
    {
        Print("Failed to close buy position. Error: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Close Sell Position                                              |
//+------------------------------------------------------------------+
void CloseSellPosition()
{
    if(trade.PositionClose(_Symbol))
    {
        Print("Sell position closed successfully");
        // DON'T reset flags here - keep waiting for opposite signal
        // Flags will only reset when opposite signal actually executes
    }
    else
    {
        Print("Failed to close sell position. Error: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Handle Trailing Stop                                             |
//+------------------------------------------------------------------+
void HandleTrailingStop()
{
    if(!PositionSelect(_Symbol))
        return;

    long position_type = PositionGetInteger(POSITION_TYPE);
    double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double position_sl = PositionGetDouble(POSITION_SL);
    double current_price = (position_type == POSITION_TYPE_BUY) ? Bid() : Ask();

    double trailing_distance = TrailingStop * _Point;
    double new_sl = 0;
    bool modify_needed = false;

    if(position_type == POSITION_TYPE_BUY)
    {
        //--- Calculate new stop loss for buy position
        new_sl = current_price - trailing_distance;

        //--- Check if we need to modify
        if(new_sl > position_sl + _Point || position_sl == 0)
        {
            modify_needed = true;
        }
    }
    else if(position_type == POSITION_TYPE_SELL)
    {
        //--- Calculate new stop loss for sell position
        new_sl = current_price + trailing_distance;

        //--- Check if we need to modify
        if(new_sl < position_sl - _Point || position_sl == 0)
        {
            modify_needed = true;
        }
    }

    //--- Modify position if needed
    if(modify_needed)
    {
        double tp = PositionGetDouble(POSITION_TP);
        if(trade.PositionModify(_Symbol, new_sl, tp))
        {
            Print("Trailing stop updated to ", new_sl);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if there are pending orders                               |
//+------------------------------------------------------------------+
bool HasPendingOrders()
{
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(OrderGetTicket(i)))
        {
            if(OrderGetString(ORDER_SYMBOL) == _Symbol &&
               OrderGetInteger(ORDER_MAGIC) == MAGIC_NUMBER)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get Ask price                                                    |
//+------------------------------------------------------------------+
double Ask()
{
    MqlTick tick;
    if(SymbolInfoTick(_Symbol, tick))
        return tick.ask;
    return 0;
}

//+------------------------------------------------------------------+
//| Get Bid price                                                    |
//+------------------------------------------------------------------+
double Bid()
{
    MqlTick tick;
    if(SymbolInfoTick(_Symbol, tick))
        return tick.bid;
    return 0;
}

//+------------------------------------------------------------------+
//| Check Distance-Based Martingale Conditions                      |
//+------------------------------------------------------------------+
void CheckMartingaleConditions()
{
    //--- Check if we have any positions
    if(!PositionSelect(_Symbol))
    {
        //--- Reset martingale flags if no positions
        ResetMartingaleFlags();
        return;
    }

    //--- If martingale already active, check for TP hit
    if(martingale_active)
    {
        CheckMartingaleTP();
        return;
    }

    //--- Check if we should trigger martingale based on distance
    if(!martingale_triggered && first_position_ticket > 0)
    {
        CheckDistanceTrigger();
    }
}

//+------------------------------------------------------------------+
//| Check if Distance should trigger Martingale                     |
//+------------------------------------------------------------------+
void CheckDistanceTrigger()
{
    double current_price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? Bid() : Ask();
    long position_type = PositionGetInteger(POSITION_TYPE);

    bool trigger_martingale = false;
    double distance_points = 0;

    if(position_type == POSITION_TYPE_BUY)
    {
        //--- For BUY: Check if price dropped by TriggerDistance points
        distance_points = (first_position_price - current_price) / _Point;
        if(distance_points >= TriggerDistance)
        {
            trigger_martingale = true;
            Print("BUY Distance trigger: ", distance_points, " points (target: ", TriggerDistance, ")");
        }
    }
    else if(position_type == POSITION_TYPE_SELL)
    {
        //--- For SELL: Check if price rose by TriggerDistance points
        distance_points = (current_price - first_position_price) / _Point;
        if(distance_points >= TriggerDistance)
        {
            trigger_martingale = true;
            Print("SELL Distance trigger: ", distance_points, " points (target: ", TriggerDistance, ")");
        }
    }

    if(trigger_martingale)
    {
        OpenMartingalePosition();
    }
}

//+------------------------------------------------------------------+
//| Open Distance-Based Martingale Position                         |
//+------------------------------------------------------------------+
void OpenMartingalePosition()
{
    long position_type = PositionGetInteger(POSITION_TYPE);
    double current_price = (position_type == POSITION_TYPE_BUY) ? Ask() : Bid();

    //--- Calculate martingale TP and final SL
    double martingale_tp, final_sl;

    if(position_type == POSITION_TYPE_BUY)
    {
        //--- BUY Martingale: TP above current price, SL below old SL
        martingale_tp = current_price + (MartingaleTP_Points * _Point);
        final_sl = first_position_sl - (FinalSL_Points * _Point);

        if(trade.Buy(MartingaleLotSize, _Symbol, current_price, final_sl, martingale_tp, "Distance Martingale Buy"))
        {
            martingale_ticket = trade.ResultOrder();
            martingale_active = true;
            martingale_triggered = true;

            //--- Modify first position SL to final SL
            if(PositionSelectByTicket(first_position_ticket))
            {
                double first_tp = PositionGetDouble(POSITION_TP);
                trade.PositionModify(_Symbol, final_sl, first_tp);
            }

            Print("Distance Martingale BUY opened: Lot=", MartingaleLotSize, " TP=", martingale_tp, " Final SL=", final_sl);
        }
    }
    else if(position_type == POSITION_TYPE_SELL)
    {
        //--- SELL Martingale: TP below current price, SL above old SL
        martingale_tp = current_price - (MartingaleTP_Points * _Point);
        final_sl = first_position_sl + (FinalSL_Points * _Point);

        if(trade.Sell(MartingaleLotSize, _Symbol, current_price, final_sl, martingale_tp, "Distance Martingale Sell"))
        {
            martingale_ticket = trade.ResultOrder();
            martingale_active = true;
            martingale_triggered = true;

            //--- Modify first position SL to final SL
            if(PositionSelectByTicket(first_position_ticket))
            {
                double first_tp = PositionGetDouble(POSITION_TP);
                trade.PositionModify(_Symbol, final_sl, first_tp);
            }

            Print("Distance Martingale SELL opened: Lot=", MartingaleLotSize, " TP=", martingale_tp, " Final SL=", final_sl);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if Martingale TP is hit and close all positions           |
//+------------------------------------------------------------------+
void CheckMartingaleTP()
{
    //--- Check if martingale position still exists
    if(!PositionSelectByTicket(martingale_ticket))
    {
        //--- Martingale position was closed - only close all if it was profitable
        //--- Check if any remaining positions are profitable before closing all
        bool has_profitable_position = false;
        for(int i = 0; i < PositionsTotal(); i++)
        {
            if(PositionGetSymbol(i) == _Symbol)
            {
                if(PositionGetDouble(POSITION_PROFIT) > 0)
                {
                    has_profitable_position = true;
                    break;
                }
            }
        }

        if(has_profitable_position)
        {
            Print("Martingale position closed with profit - closing all remaining positions");
            CloseAllPositions();
        }
        else
        {
            Print("Martingale position closed at loss - keeping remaining positions");
        }

        ResetMartingaleFlags();
        return;
    }

    //--- Check if martingale reached TP level (more precise check)
    if(PositionSelectByTicket(martingale_ticket))
    {
        double position_open = PositionGetDouble(POSITION_PRICE_OPEN);
        double position_tp = PositionGetDouble(POSITION_TP);
        long position_type = PositionGetInteger(POSITION_TYPE);
        double current_price = (position_type == POSITION_TYPE_BUY) ? Bid() : Ask();

        //--- Check if price reached TP level (with small buffer for spread)
        bool tp_reached = false;
        double buffer = 5 * _Point; // 5 point buffer

        if(position_type == POSITION_TYPE_BUY && current_price >= (position_tp - buffer))
            tp_reached = true;
        else if(position_type == POSITION_TYPE_SELL && current_price <= (position_tp + buffer))
            tp_reached = true;

        if(tp_reached)
        {
            Print("Martingale TP level reached at ", current_price, " (TP: ", position_tp, ") - closing all positions");
            CloseAllPositions();
            ResetMartingaleFlags();
        }
    }
}

//+------------------------------------------------------------------+
//| Close All Positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    //--- Close all positions for this symbol
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            ulong ticket = PositionGetTicket(i);
            if(ticket > 0)
            {
                trade.PositionClose(ticket);
                Print("Closed position ticket: ", ticket);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Reset Martingale Flags                                          |
//+------------------------------------------------------------------+
void ResetMartingaleFlags()
{
    martingale_active = false;
    first_position_price = 0;
    first_position_sl = 0;
    first_position_ticket = 0;
    martingale_ticket = 0;
    martingale_triggered = false;
    Print("Distance Martingale flags reset");
}
